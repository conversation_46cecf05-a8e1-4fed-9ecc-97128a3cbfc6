import { getImUserList } from '@/api/im/imuser'

/**
 * 群成员数据缓存
 */
const memberCache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

/**
 * 获取群成员信息
 * @param {Object} group - 群组信息
 * @param {boolean} useCache - 是否使用缓存
 * @returns {Promise<Array>} 群成员列表
 */
export async function getGroupMembers(group, useCache = true) {
  if (!group || !group.FromID || !Array.isArray(group.FromID)) {
    console.warn('群组信息不完整或FromID不是数组:', group)
    return []
  }

  if (group.FromID.length === 0) {
    console.warn('群组成员列表为空')
    return []
  }

  const cacheKey = `group_${group.ID}_members`
  const now = Date.now()

  // 检查缓存
  if (useCache && memberCache.has(cacheKey)) {
    const cached = memberCache.get(cacheKey)
    if (now - cached.timestamp < CACHE_DURATION) {
      console.log('使用缓存的群成员数据')
      return cached.data
    }
  }

  try {
    // 调用用户列表接口获取所有用户
    const response = await getImUserList({
      page: 1,
      pageSize: 1000 // 获取足够多的用户数据
    })

    if (response.code === 0 && response.data && response.data.list) {
      const allUsers = response.data.list
      const groupMemberIds = group.FromID

      console.log('所有用户数据:', allUsers.length, '群成员ID:', groupMemberIds)

      // 筛选出群成员
      const groupMembers = allUsers.filter(user =>
        groupMemberIds.includes(user.id)
      ).map(user => ({
        id: user.id,
        nickname: user.name || user.iphoneNum || `用户${user.id}`,
        avatar: user.headImg || '',
        online: user.online || false,
        isAdmin: group.Admins && Array.isArray(group.Admins) && group.Admins.includes(user.id),
        isOwner: group.GroupHave === user.id,
        phone: user.iphoneNum,
        lastLoginTime: user.lastLoginTime,
        joinTime: user.createdAt,
        userConfig: user.userConfig,
        rawData: user // 保存原始用户数据
      }))

      // 按照群主、管理员、普通成员的顺序排序
      groupMembers.sort((a, b) => {
        if (a.isOwner && !b.isOwner) return -1
        if (!a.isOwner && b.isOwner) return 1
        if (a.isAdmin && !b.isAdmin) return -1
        if (!a.isAdmin && b.isAdmin) return 1
        // 按昵称排序
        return a.nickname.localeCompare(b.nickname)
      })

      // 缓存结果
      memberCache.set(cacheKey, {
        data: groupMembers,
        timestamp: now
      })

      console.log('群成员数据:', groupMembers)

      if (groupMembers.length === 0) {
        console.warn('未找到匹配的群成员，群成员ID:', groupMemberIds, '用户列表:', allUsers.map(u => u.id))
      }

      return groupMembers
    } else {
      console.error('获取用户列表失败:', response)
      throw new Error('获取用户列表失败')
    }
  } catch (error) {
    console.error('获取群成员信息出错:', error)
    throw error
  }
}

/**
 * 清除群成员缓存
 * @param {number} groupId - 群组ID，如果不提供则清除所有缓存
 */
export function clearMemberCache(groupId = null) {
  if (groupId) {
    const cacheKey = `group_${groupId}_members`
    memberCache.delete(cacheKey)
    console.log(`已清除群组 ${groupId} 的成员缓存`)
  } else {
    memberCache.clear()
    console.log('已清除所有群成员缓存')
  }
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
export function getCacheStats() {
  const now = Date.now()
  let validCount = 0
  let expiredCount = 0

  for (const [key, value] of memberCache.entries()) {
    if (now - value.timestamp < CACHE_DURATION) {
      validCount++
    } else {
      expiredCount++
    }
  }

  return {
    total: memberCache.size,
    valid: validCount,
    expired: expiredCount
  }
}

/**
 * 清理过期缓存
 */
export function cleanExpiredCache() {
  const now = Date.now()
  const keysToDelete = []

  for (const [key, value] of memberCache.entries()) {
    if (now - value.timestamp >= CACHE_DURATION) {
      keysToDelete.push(key)
    }
  }

  keysToDelete.forEach(key => memberCache.delete(key))
  
  if (keysToDelete.length > 0) {
    console.log(`已清理 ${keysToDelete.length} 个过期缓存`)
  }

  return keysToDelete.length
}

/**
 * 格式化成员角色
 * @param {Object} member - 成员信息
 * @returns {string} 角色描述
 */
export function formatMemberRole(member) {
  if (member.isOwner) return '群主'
  if (member.isAdmin) return '管理员'
  return '普通成员'
}

/**
 * 格式化在线状态
 * @param {boolean} online - 在线状态
 * @returns {Object} 状态信息
 */
export function formatOnlineStatus(online) {
  return {
    text: online ? '在线' : '离线',
    type: online ? 'success' : 'info',
    color: online ? '#22c55e' : '#9ca3af'
  }
}

/**
 * 直接从缓存中获取群成员信息（不调用API）
 * @param {string|number} groupId - 群组ID
 * @returns {Array} 群成员列表，如果缓存中没有则返回空数组
 */
export function getGroupMembersFromCache(groupId) {
  if (!groupId) {
    console.warn('群组ID为空')
    return []
  }

  const cacheKey = `group_${groupId}_members`
  const now = Date.now()

  if (memberCache.has(cacheKey)) {
    const cached = memberCache.get(cacheKey)
    if (now - cached.timestamp < CACHE_DURATION) {
      console.log('从缓存获取群成员数据:', cached.data.length, '个成员')
      return cached.data
    } else {
      console.log('群成员缓存已过期')
      memberCache.delete(cacheKey)
    }
  }

  console.log('缓存中没有找到群成员数据:', groupId)
  return []
}

/**
 * 从缓存的群成员中查找指定用户
 * @param {string|number} groupId - 群组ID
 * @param {string|number} userId - 用户ID
 * @returns {Object|null} 用户信息或null
 */
export function findMemberInCache(groupId, userId) {
  if (!groupId || !userId) {
    return null
  }

  const members = getGroupMembersFromCache(groupId)
  const member = members.find(m => m.id == userId)

  if (member) {
    console.log('在群成员缓存中找到用户:', { userId, groupId, member })
    return {
      id: String(userId),
      nickname: member.nickname,
      avatar: member.avatar || '',
      originalAvatar: member.avatar || '',
      isOwner: member.isOwner,
      isAdmin: member.isAdmin,
      online: member.online
    }
  }

  console.log('在群成员缓存中未找到用户:', { userId, groupId })
  return null
}

// 定期清理过期缓存
setInterval(cleanExpiredCache, 60 * 1000) // 每分钟清理一次
